# Fix: Notification Mark as Read Permission Issue

## Vấn đề

<PERSON>hi admin cố gắng mark as read một thông báo, hệ thống trả về lỗi:

```json
{
  "error": "Không có quyền cập nhật thông báo này"
}
```

## Nguyên nhân

Trong API endpoint `PUT /api/admin/notifications/[id]`, logic kiểm tra quyền truy cập thiếu điều kiện cho Admin role:

**Trước khi sửa (dòng 95-98):**
```typescript
const hasAccess = 
  notification.targetType === "ALL_ADMINS" ||
  (notification.targetType === "SPECIFIC_ADMIN" && notification.targetId === session.user.id) ||
  notification.targetType === `ROLE_${session.user.role}`;
```

**So sánh với GET endpoint (dòng 42-46) - c<PERSON> điều kiện Admin:**
```typescript
const hasAccess = 
  notification.targetType === "ALL_ADMINS" ||
  (notification.targetType === "SPECIFIC_ADMIN" && notification.targetId === session.user.id) ||
  notification.targetType === `ROLE_${session.user.role}` ||
  session.user.role === "ADMIN"; // Admin có thể xem tất cả thông báo
```

## Giải pháp

### 1. Sửa API endpoint PUT `/api/admin/notifications/[id]`

**File:** `src/app/api/admin/notifications/[id]/route.ts`

```typescript
// Check if user has access to this notification
const hasAccess =
  notification.targetType === "ALL_ADMINS" ||
  (notification.targetType === "SPECIFIC_ADMIN" &&
    notification.targetId === session.user.id) ||
  notification.targetType === `ROLE_${session.user.role}` ||
  session.user.role === "ADMIN"; // Admin can update all notifications
```

### 2. Sửa bulk operations

**File:** `src/app/api/admin/notifications/bulk/route.ts`

```typescript
return (
  notification.targetType === "ALL_ADMINS" ||
  (notification.targetType === "SPECIFIC_ADMIN" &&
    notification.targetId === adminUser.id) ||
  notification.targetType === `ROLE_${adminUser.role}` ||
  adminUser.role === "ADMIN" // Admin can access all notifications
);
```

## Kiểm tra

### Test thành công

```bash
curl -X PUT 'http://localhost:6002/api/admin/notifications/cmdeww6wv00me4qylv6wwdfzn' \
  -H 'Content-Type: application/json' \
  -H 'Cookie: admin-session=...' \
  --data-raw '{"isRead":true}'
```

**Response:**
```json
{
  "message": "Cập nhật thông báo thành công",
  "notification": {
    "id": "cmdeww6wv00me4qylv6wwdfzn",
    "isRead": true,
    "readAt": "2025-07-22T19:46:07.539Z",
    ...
  }
}
```

## Logic quyền truy cập

Sau khi sửa, các trường hợp được phép mark as read:

1. **ALL_ADMINS**: Tất cả admin có thể mark as read
2. **SPECIFIC_ADMIN**: Chỉ admin được chỉ định có thể mark as read
3. **ROLE_BASED**: Admin có role tương ứng có thể mark as read
4. **ADMIN role**: Admin có thể mark as read tất cả thông báo (bất kể targetType)

## Tác động

- ✅ Admin có thể mark as read tất cả thông báo
- ✅ Moderator chỉ có thể mark as read thông báo dành cho họ
- ✅ Logic nhất quán giữa GET và PUT endpoints
- ✅ Bulk operations cũng được sửa tương tự

## Files đã thay đổi

1. `src/app/api/admin/notifications/[id]/route.ts` - Thêm điều kiện Admin cho PUT endpoint
2. `src/app/api/admin/notifications/bulk/route.ts` - Thêm điều kiện Admin cho bulk operations
