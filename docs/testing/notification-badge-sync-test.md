# Test Đồng Bộ Badge Thông Báo

## Mô tả
Test này kiểm tra tính đồng bộ của badge số lượng thông báo chưa đọc trên tất cả các component trong hệ thống admin.

## Các Component Cần Test

### 1. NotificationBell (Header)
- **Vị trí**: Header admin
- **Component**: `src/components/admin/notifications/NotificationBell.tsx`
- **Badge**: Hiển thị số lượng thông báo chưa đọc
- **Source**: `useNotifications()` context

### 2. NotificationDropdown
- **Vị trí**: Dropdown từ NotificationBell
- **Component**: `src/components/admin/notifications/NotificationDropdown.tsx`
- **Badge**: Hiển thị số lượng trong header dropdown
- **Source**: `useNotifications()` context

### 3. Enhanced Sidebar
- **Vị trí**: Sidebar admin (enhanced version)
- **Component**: `src/components/admin/enhanced-sidebar.tsx`
- **Badge**: Badge cho menu "Thông báo"
- **Source**: `useNotifications()` context

### 4. Regular Sidebar
- **Vị trí**: Sidebar admin (regular version)
- **Component**: `src/components/admin/sidebar.tsx`
- **Badge**: Badge cho menu "Thông báo"
- **Source**: `useNotifications()` context

### 5. Notifications Page
- **Vị trí**: Trang `/admin/notifications`
- **Component**: `src/app/admin/notifications/page.tsx`
- **Badge**: Hiển thị số lượng trong UI
- **Source**: `useNotifications()` context (đã sửa từ tính toán cục bộ)

## Test Cases

### Test Case 1: Khởi tạo ban đầu
**Mục tiêu**: Kiểm tra tất cả badge hiển thị cùng số lượng khi load trang

**Bước thực hiện**:
1. Đăng nhập admin
2. Kiểm tra số lượng badge trên:
   - NotificationBell trong header
   - Menu "Thông báo" trong sidebar
   - Dropdown thông báo (nếu mở)
   - Trang notifications (nếu truy cập)

**Kết quả mong đợi**: Tất cả badge hiển thị cùng một số

### Test Case 2: Mark as Read
**Mục tiêu**: Kiểm tra đồng bộ khi đánh dấu thông báo đã đọc

**Bước thực hiện**:
1. Ghi nhận số lượng badge ban đầu
2. Đánh dấu 1 thông báo là đã đọc
3. Kiểm tra tất cả badge sau 1 giây

**Kết quả mong đợi**: Tất cả badge giảm 1 đơn vị

### Test Case 3: Mark All as Read
**Mục tiêu**: Kiểm tra đồng bộ khi đánh dấu tất cả đã đọc

**Bước thực hiện**:
1. Đảm bảo có thông báo chưa đọc
2. Click "Đánh dấu tất cả đã đọc"
3. Kiểm tra tất cả badge ngay lập tức và sau 1 giây

**Kết quả mong đợi**: 
- Ngay lập tức: Tất cả badge = 0 (optimistic update)
- Sau 1 giây: Vẫn = 0 (server sync)

### Test Case 4: Tạo thông báo mới
**Mục tiêu**: Kiểm tra đồng bộ khi có thông báo mới

**Bước thực hiện**:
1. Ghi nhận số lượng badge ban đầu
2. Tạo thông báo mới (qua admin panel)
3. Kiểm tra tất cả badge sau vài giây

**Kết quả mong đợi**: Tất cả badge tăng 1 đơn vị

### Test Case 5: Real-time Update (SSE)
**Mục tiêu**: Kiểm tra cập nhật real-time qua SSE

**Bước thực hiện**:
1. Mở 2 tab admin
2. Tạo thông báo ở tab 1
3. Kiểm tra badge ở tab 2

**Kết quả mong đợi**: Badge ở tab 2 cập nhật tự động

## Automated Test Script

```typescript
// Test helper để kiểm tra đồng bộ badge
export const testNotificationBadgeSync = async () => {
  const badges = {
    notificationBell: document.querySelector('[data-testid="notification-bell-badge"]'),
    sidebarMenu: document.querySelector('[data-testid="sidebar-notification-badge"]'),
    dropdownHeader: document.querySelector('[data-testid="dropdown-notification-badge"]'),
  };

  const counts = Object.entries(badges).map(([key, element]) => ({
    component: key,
    count: element ? parseInt(element.textContent || '0') : 0
  }));

  const isSync = counts.every(item => item.count === counts[0].count);
  
  return {
    isSync,
    counts,
    message: isSync ? 'All badges are synchronized' : 'Badge synchronization failed'
  };
};
```

## Các Cải Tiến Đã Thực Hiện

### 1. Unified Source of Truth
- Tất cả component sử dụng `useNotifications()` context
- Loại bỏ tính toán `unreadCount` cục bộ

### 2. Optimistic Updates
- `markAsRead`: Cập nhật ngay lập tức, sync sau 500ms
- `markAllAsRead`: Cập nhật ngay lập tức, sync sau 500ms
- `markAsUnread`: Cập nhật ngay lập tức, sync sau 500ms

### 3. SSE Improvements
- Cập nhật `unreadCount` khi nhận `latest_notifications`
- Đảm bảo đồng bộ giữa notifications và unreadCount

### 4. Consistent Badge Display
- Tất cả badge hiển thị "99+" khi > 99
- Chỉ hiển thị badge khi có thông báo chưa đọc
- Styling nhất quán across components

## Lưu Ý Khi Test

1. **Network Delay**: SSE có thể có độ trễ, chờ 2-3 giây để kiểm tra
2. **Browser Cache**: Clear cache nếu thấy kết quả không nhất quán
3. **Multiple Tabs**: Test với nhiều tab để kiểm tra real-time sync
4. **Error Handling**: Test khi mất kết nối SSE
5. **Performance**: Kiểm tra không có memory leak khi long polling

## Kết Luận

Sau khi thực hiện các cải tiến:
- ✅ Tất cả badge sử dụng cùng source of truth
- ✅ Optimistic updates cho UX tốt hơn
- ✅ Real-time sync qua SSE
- ✅ Consistent styling và behavior
- ✅ Error handling và fallback mechanisms
