import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { PUT } from "@/app/api/admin/notifications/[id]/route";
import { prisma } from "@/lib/prisma";

// Mock admin auth options
jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<
  typeof getServerSession
>;

describe("Notification Mark as Read Fix", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      email: "<EMAIL>",
      name: "NS Shop Admin",
      role: "ADMIN",
      type: "admin" as const,
    },
  };

  const mockModeratorSession = {
    user: {
      id: "moderator-456",
      email: "<EMAIL>",
      name: "NS Shop Moderator",
      role: "MODERATOR",
      type: "admin" as const,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Admin Role Access", () => {
    it("should allow ADMIN to mark any notification as read", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Mock notification that is targeted to a specific admin (not current admin)
      const mockNotification = {
        id: "notification-123",
        title: "Test Notification",
        message: "Test message",
        type: "INFO",
        priority: "NORMAL",
        targetType: "SPECIFIC_ADMIN",
        targetId: "other-admin-789", // Different from current admin
        isRead: false,
        readAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockUpdatedNotification = {
        ...mockNotification,
        isRead: true,
        readAt: new Date(),
      };

      (prisma.notification.findUnique as jest.Mock).mockResolvedValue(
        mockNotification
      );
      (prisma.notification.update as jest.Mock).mockResolvedValue(
        mockUpdatedNotification
      );

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/notification-123",
        {
          method: "PUT",
          body: JSON.stringify({ isRead: true }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const response = await PUT(request, {
        params: { id: "notification-123" },
      });

      expect(response.status).toBe(200);
      expect(prisma.notification.update).toHaveBeenCalledWith({
        where: { id: "notification-123" },
        data: {
          isRead: true,
          readAt: expect.any(Date),
        },
      });
    });

    it("should allow ADMIN to mark ALL_ADMINS notification as read", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const mockNotification = {
        id: "notification-456",
        title: "All Admins Notification",
        message: "For all admins",
        type: "INFO",
        priority: "NORMAL",
        targetType: "ALL_ADMINS",
        targetId: null,
        isRead: false,
        readAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockUpdatedNotification = {
        ...mockNotification,
        isRead: true,
        readAt: new Date(),
      };

      (prisma.notification.findUnique as jest.Mock).mockResolvedValue(
        mockNotification
      );
      (prisma.notification.update as jest.Mock).mockResolvedValue(
        mockUpdatedNotification
      );

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/notification-456",
        {
          method: "PUT",
          body: JSON.stringify({ isRead: true }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const response = await PUT(request, {
        params: { id: "notification-456" },
      });

      expect(response.status).toBe(200);
    });
  });

  describe("Moderator Role Access", () => {
    it("should allow MODERATOR to mark their own targeted notification as read", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      const mockNotification = {
        id: "notification-789",
        title: "Moderator Notification",
        message: "For specific moderator",
        type: "INFO",
        priority: "NORMAL",
        targetType: "SPECIFIC_ADMIN",
        targetId: "moderator-456", // Same as current moderator
        isRead: false,
        readAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockUpdatedNotification = {
        ...mockNotification,
        isRead: true,
        readAt: new Date(),
      };

      (prisma.notification.findUnique as jest.Mock).mockResolvedValue(
        mockNotification
      );
      (prisma.notification.update as jest.Mock).mockResolvedValue(
        mockUpdatedNotification
      );

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/notification-789",
        {
          method: "PUT",
          body: JSON.stringify({ isRead: true }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const response = await PUT(request, {
        params: { id: "notification-789" },
      });

      expect(response.status).toBe(200);
    });

    it("should NOT allow MODERATOR to mark other admin's notification as read", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      const mockNotification = {
        id: "notification-999",
        title: "Other Admin Notification",
        message: "For different admin",
        type: "INFO",
        priority: "NORMAL",
        targetType: "SPECIFIC_ADMIN",
        targetId: "other-admin-123", // Different from current moderator
        isRead: false,
        readAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (prisma.notification.findUnique as jest.Mock).mockResolvedValue(
        mockNotification
      );

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/notification-999",
        {
          method: "PUT",
          body: JSON.stringify({ isRead: true }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const response = await PUT(request, {
        params: { id: "notification-999" },
      });

      expect(response.status).toBe(403);
      const data = await response.json();
      expect(data.error).toBe("Không có quyền cập nhật thông báo này");
    });

    it("should allow MODERATOR to mark ROLE_MODERATOR notification as read", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      const mockNotification = {
        id: "notification-role",
        title: "Role-based Notification",
        message: "For all moderators",
        type: "INFO",
        priority: "NORMAL",
        targetType: "ROLE_MODERATOR",
        targetId: null,
        isRead: false,
        readAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockUpdatedNotification = {
        ...mockNotification,
        isRead: true,
        readAt: new Date(),
      };

      (prisma.notification.findUnique as jest.Mock).mockResolvedValue(
        mockNotification
      );
      (prisma.notification.update as jest.Mock).mockResolvedValue(
        mockUpdatedNotification
      );

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/notification-role",
        {
          method: "PUT",
          body: JSON.stringify({ isRead: true }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const response = await PUT(request, {
        params: { id: "notification-role" },
      });

      expect(response.status).toBe(200);
    });
  });

  describe("Error Cases", () => {
    it("should return 404 for non-existent notification", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);
      (prisma.notification.findUnique as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/non-existent",
        {
          method: "PUT",
          body: JSON.stringify({ isRead: true }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const response = await PUT(request, { params: { id: "non-existent" } });

      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.error).toBe("Không tìm thấy thông báo");
    });

    it("should return 403 for unauthenticated request", async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/notification-123",
        {
          method: "PUT",
          body: JSON.stringify({ isRead: true }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const response = await PUT(request, {
        params: { id: "notification-123" },
      });

      expect(response.status).toBe(403);
      const data = await response.json();
      expect(data.error).toBe("Không có quyền truy cập");
    });
  });
});
