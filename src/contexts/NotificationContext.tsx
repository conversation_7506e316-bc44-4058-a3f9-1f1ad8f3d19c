"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
} from "react";
import { useAdminAuth } from "@/contexts/AdminAuthContext";
import { Notification, NotificationContextType } from "@/types/notification";

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
}

// Additional hook for notification utilities
export function useNotificationUtils() {
  const { createNotification } = useNotifications();

  const showSuccess = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "SUCCESS",
      priority: "NORMAL",
      ...options,
    });
  };

  const showError = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "ERROR",
      priority: "HIGH",
      ...options,
    });
  };

  const showWarning = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "WARNING",
      priority: "NORMAL",
      ...options,
    });
  };

  const showInfo = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "INFO",
      priority: "LOW",
      ...options,
    });
  };

  const showSystemAlert = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      priority?: "LOW" | "NORMAL" | "HIGH" | "URGENT";
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "SYSTEM",
      priority: options?.priority || "HIGH",
      targetType: "ALL_ADMINS",
      actionUrl: options?.actionUrl,
      metadata: options?.metadata,
    });
  };

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showSystemAlert,
  };
}

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const { adminUser } = useAdminAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);

  // Fetch notifications manually
  const refreshNotifications = useCallback(async () => {
    if (!adminUser || adminUser.type !== "admin") {
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/notifications?limit=10");

      if (!response.ok) {
        throw new Error("Failed to fetch notifications");
      }

      const data = await response.json();
      setNotifications(data.notifications || []);

      // Calculate unread count
      const unread = (data.notifications || []).filter(
        (n: Notification) => !n.isRead
      ).length;
      setUnreadCount(unread);

      setError(null);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch notifications"
      );
    } finally {
      setIsLoading(false);
    }
  }, [adminUser]);

  // Initialize SSE connection
  const initializeSSE = useCallback(() => {
    if (!adminUser || adminUser.type !== "admin") {
      return;
    }

    // Close existing connection
    if (eventSource) {
      eventSource.close();
    }

    const newEventSource = new EventSource("/api/admin/notifications/stream");

    newEventSource.onopen = () => {
      setIsConnected(true);
      setError(null);
      console.log("Notification stream connected");
      // Clear fallback timeout since SSE connected successfully
      clearTimeout(fallbackTimeout);
    };

    // Fallback: if SSE doesn't connect within 5 seconds, fetch manually
    const fallbackTimeout = setTimeout(() => {
      if (!isConnected) {
        console.log("SSE connection timeout, falling back to manual fetch");
        refreshNotifications();
      }
    }, 5000);

    newEventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case "connection":
            console.log("Notification stream established");
            break;

          case "unread_count":
            setUnreadCount(data.count);
            break;

          case "latest_notifications":
            setNotifications(data.notifications);
            // Also update unread count from the received notifications
            const unread = (data.notifications || []).filter(
              (n: Notification) => !n.isRead
            ).length;
            setUnreadCount(unread);
            setIsLoading(false);
            break;

          case "heartbeat":
            // Keep connection alive
            break;

          default:
            console.log("Unknown notification event:", data);
        }
      } catch (err) {
        console.error("Error parsing SSE data:", err);
      }
    };

    newEventSource.onerror = (event) => {
      console.error("Notification stream error:", event);
      setIsConnected(false);
      setError("Connection to notification stream failed");

      // Clear fallback timeout since we got an error
      clearTimeout(fallbackTimeout);

      // Fallback to manual fetch immediately on error
      refreshNotifications();

      // Attempt to reconnect after 5 seconds
      setTimeout(() => {
        if (adminUser && adminUser.type === "admin") {
          initializeSSE();
        }
      }, 5000);
    };

    setEventSource(newEventSource);
  }, [adminUser, eventSource]);

  // Initialize connection when admin user is available
  useEffect(() => {
    if (adminUser && adminUser.type === "admin") {
      // First, fetch notifications immediately
      refreshNotifications();
      // Then try to establish SSE connection
      initializeSSE();
    }

    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [adminUser, refreshNotifications]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, []);

  // Mark notification as read
  const markAsRead = useCallback(
    async (notificationId: string) => {
      try {
        // Check if notification is already read to prevent unnecessary API calls
        const notification = notifications.find((n) => n.id === notificationId);
        if (notification?.isRead) {
          return; // Already read, no need to make API call
        }

        const response = await fetch(
          `/api/admin/notifications/${notificationId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isRead: true }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to mark notification as read");
        }

        // Update local state optimistically
        setNotifications((prev) =>
          prev.map((n) =>
            n.id === notificationId
              ? { ...n, isRead: true, readAt: new Date().toISOString() }
              : n
          )
        );

        setUnreadCount((prev) => Math.max(0, prev - 1));

        // Refresh after a short delay to ensure server state consistency
        setTimeout(() => {
          refreshNotifications();
        }, 500);
      } catch (err) {
        console.error("Error marking notification as read:", err);
        throw err;
      }
    },
    [notifications, refreshNotifications]
  );

  // Mark notification as unread
  const markAsUnread = useCallback(
    async (notificationId: string) => {
      try {
        // Check if notification is already unread to prevent unnecessary API calls
        const notification = notifications.find((n) => n.id === notificationId);
        if (!notification?.isRead) {
          return; // Already unread, no need to make API call
        }

        const response = await fetch(
          `/api/admin/notifications/${notificationId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isRead: false }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to mark notification as unread");
        }

        // Update local state optimistically
        setNotifications((prev) =>
          prev.map((n) =>
            n.id === notificationId
              ? { ...n, isRead: false, readAt: undefined }
              : n
          )
        );

        setUnreadCount((prev) => prev + 1);

        // Refresh after a short delay to ensure server state consistency
        setTimeout(() => {
          refreshNotifications();
        }, 500);
      } catch (err) {
        console.error("Error marking notification as unread:", err);
        throw err;
      }
    },
    [notifications, refreshNotifications]
  );

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const unreadIds = notifications.filter((n) => !n.isRead).map((n) => n.id);

      if (unreadIds.length === 0) {
        return;
      }

      // Optimistic update - mark all as read immediately
      setNotifications((prev) =>
        prev.map((n) =>
          unreadIds.includes(n.id)
            ? { ...n, isRead: true, readAt: new Date().toISOString() }
            : n
        )
      );
      setUnreadCount(0);

      const response = await fetch("/api/admin/notifications/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "mark_read",
          notificationIds: unreadIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark all notifications as read");
      }

      // Refresh after a short delay to ensure server state consistency
      setTimeout(() => {
        refreshNotifications();
      }, 500);
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
      // Revert optimistic update on error
      refreshNotifications();
      throw err;
    }
  }, [notifications, refreshNotifications]);

  // Delete notification
  const deleteNotification = useCallback(
    async (notificationId: string) => {
      try {
        const response = await fetch(
          `/api/admin/notifications/${notificationId}`,
          {
            method: "DELETE",
          }
        );

        if (!response.ok) {
          throw new Error("Failed to delete notification");
        }

        // Refresh notifications from server to ensure data consistency
        await refreshNotifications();
      } catch (err) {
        console.error("Error deleting notification:", err);
        throw err;
      }
    },
    [refreshNotifications]
  );

  // Create new notification
  const createNotification = useCallback(
    async (notification: Partial<Notification>) => {
      try {
        const response = await fetch("/api/admin/notifications", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(notification),
        });

        if (!response.ok) {
          throw new Error("Failed to create notification");
        }

        const data = await response.json();

        // Add to local state if it targets current user
        const newNotification = data.notification;
        const shouldShow =
          newNotification.targetType === "ALL_ADMINS" ||
          (newNotification.targetType === "SPECIFIC_ADMIN" &&
            newNotification.targetId === adminUser?.id) ||
          newNotification.targetType === `ROLE_${adminUser?.role}`;

        if (shouldShow) {
          setNotifications((prev) => [newNotification, ...prev]);
          if (!newNotification.isRead) {
            setUnreadCount((prev) => prev + 1);
          }
        }
      } catch (err) {
        console.error("Error creating notification:", err);
        throw err;
      }
    },
    [adminUser]
  );

  const value: NotificationContextType = useMemo(
    () => ({
      notifications,
      unreadCount,
      isConnected,
      isLoading,
      error,
      markAsRead,
      markAsUnread,
      markAllAsRead,
      deleteNotification,
      refreshNotifications,
      createNotification,
    }),
    [
      notifications,
      unreadCount,
      isConnected,
      isLoading,
      error,
      markAsRead,
      markAsUnread,
      markAllAsRead,
      deleteNotification,
      refreshNotifications,
      createNotification,
    ]
  );

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}
