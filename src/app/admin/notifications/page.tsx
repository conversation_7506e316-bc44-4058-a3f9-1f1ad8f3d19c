"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Bell,
  Search,
  Plus,
  MoreHorizontal,
  Check,
  X,
  Trash2,
  Filter,
  RefreshCw,
  AlertCircle,
  Info,
  CheckCircle,
  AlertTriangle,
  XCir<PERSON>,
  Settings,
} from "lucide-react";
import { CreateNotificationDialog } from "@/components/admin/create-notification-dialog";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";
import { toast } from "sonner";
import { Notification } from "@/types/notification";
import { useNotifications } from "@/contexts/NotificationContext";

export default function NotificationsPage() {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [unreadOnly, setUnreadOnly] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>(
    []
  );
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const isAdmin = session?.user?.role === "ADMIN";

  useEffect(() => {
    fetchNotifications();
  }, [search, selectedType, unreadOnly]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (search) params.append("search", search);
      if (selectedType && selectedType !== "all")
        params.append("type", selectedType);
      if (unreadOnly) params.append("unreadOnly", "true");

      const response = await fetch(`/api/admin/notifications?${params}`);
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      toast.error("Có lỗi xảy ra khi tải thông báo");
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(
        `/api/admin/notifications/${notificationId}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ isRead: true }),
        }
      );

      if (response.ok) {
        fetchNotifications();
        toast.success("Đã đánh dấu là đã đọc");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra");
    }
  };

  const handleBulkAction = async (
    action: "mark_read" | "mark_unread" | "delete"
  ) => {
    if (selectedNotifications.length === 0) {
      toast.error("Vui lòng chọn ít nhất một thông báo");
      return;
    }

    try {
      const response = await fetch("/api/admin/notifications/bulk", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action,
          notificationIds: selectedNotifications,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        fetchNotifications();
        setSelectedNotifications([]);
        toast.success(data.message);
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra");
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "SUCCESS":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "WARNING":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case "ERROR":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "SYSTEM":
        return <Settings className="h-4 w-4 text-blue-600" />;
      default:
        return <Info className="h-4 w-4 text-blue-600" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      INFO: "default",
      SUCCESS: "default",
      WARNING: "secondary",
      ERROR: "destructive",
      SYSTEM: "outline",
    };
    return (
      <Badge variant={variants[type as keyof typeof variants] as any}>
        {type}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      LOW: "bg-gray-100 text-gray-800",
      NORMAL: "bg-blue-100 text-blue-800",
      HIGH: "bg-orange-100 text-orange-800",
      URGENT: "bg-red-100 text-red-800",
    };
    return (
      <Badge className={colors[priority as keyof typeof colors]}>
        {priority}
      </Badge>
    );
  };

  // Use unreadCount from context instead of calculating locally
  const { unreadCount: contextUnreadCount } = useNotifications();
  const unreadCount = contextUnreadCount;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Bell className="h-8 w-8" />
            Thông báo
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </h1>
          <p className="text-muted-foreground">
            Quản lý thông báo hệ thống và cá nhân
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchNotifications}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </Button>
          {isAdmin && (
            <Button onClick={() => setCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Tạo thông báo
            </Button>
          )}
        </div>
      </div>

      {/* Filters and Bulk Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Bộ lọc và thao tác</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4 mb-4">
            <div className="relative flex-1 min-w-[200px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm thông báo..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Loại" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="INFO">Info</SelectItem>
                <SelectItem value="SUCCESS">Success</SelectItem>
                <SelectItem value="WARNING">Warning</SelectItem>
                <SelectItem value="ERROR">Error</SelectItem>
                <SelectItem value="SYSTEM">System</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="unread-only"
                checked={unreadOnly}
                onCheckedChange={setUnreadOnly}
              />
              <label htmlFor="unread-only" className="text-sm">
                Chỉ chưa đọc
              </label>
            </div>
          </div>

          {selectedNotifications.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <span className="text-sm">
                Đã chọn {selectedNotifications.length} thông báo
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction("mark_read")}
              >
                <Check className="h-4 w-4 mr-1" />
                Đánh dấu đã đọc
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction("mark_unread")}
              >
                <X className="h-4 w-4 mr-1" />
                Đánh dấu chưa đọc
              </Button>
              {isAdmin && (
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleBulkAction("delete")}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Xóa
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notifications List */}
      <div className="space-y-4">
        {loading ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Đang tải...</p>
            </CardContent>
          </Card>
        ) : notifications.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Bell className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Không có thông báo</h3>
              <p className="text-muted-foreground">
                Chưa có thông báo nào phù hợp với bộ lọc
              </p>
            </CardContent>
          </Card>
        ) : (
          notifications.map((notification) => (
            <Card
              key={notification.id}
              className={`transition-all hover:shadow-md ${
                !notification.isRead
                  ? "border-l-4 border-l-primary bg-primary/5"
                  : ""
              }`}
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  <Checkbox
                    checked={selectedNotifications.includes(notification.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedNotifications([
                          ...selectedNotifications,
                          notification.id,
                        ]);
                      } else {
                        setSelectedNotifications(
                          selectedNotifications.filter(
                            (id) => id !== notification.id
                          )
                        );
                      }
                    }}
                  />

                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(notification.type)}
                        <h3
                          className={`font-medium ${
                            !notification.isRead ? "font-semibold" : ""
                          }`}
                        >
                          {notification.title}
                        </h3>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {getTypeBadge(notification.type)}
                        {getPriorityBadge(notification.priority)}
                      </div>
                    </div>

                    <p className="text-muted-foreground">
                      {notification.message}
                    </p>

                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center space-x-4">
                        <span>
                          {formatDistanceToNow(
                            new Date(notification.createdAt),
                            {
                              addSuffix: true,
                              locale: vi,
                            }
                          )}
                        </span>
                        {notification.creator && (
                          <span>bởi {notification.creator.name}</span>
                        )}
                        {notification.expiresAt && (
                          <span className="text-orange-600">
                            Hết hạn{" "}
                            {formatDistanceToNow(
                              new Date(notification.expiresAt),
                              {
                                addSuffix: true,
                                locale: vi,
                              }
                            )}
                          </span>
                        )}
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {!notification.isRead ? (
                            <DropdownMenuItem
                              onClick={() => handleMarkAsRead(notification.id)}
                            >
                              <Check className="h-4 w-4 mr-2" />
                              Đánh dấu đã đọc
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem
                              onClick={() => {
                                fetch(
                                  `/api/admin/notifications/${notification.id}`,
                                  {
                                    method: "PUT",
                                    headers: {
                                      "Content-Type": "application/json",
                                    },
                                    body: JSON.stringify({ isRead: false }),
                                  }
                                ).then(() => fetchNotifications());
                              }}
                            >
                              <X className="h-4 w-4 mr-2" />
                              Đánh dấu chưa đọc
                            </DropdownMenuItem>
                          )}
                          {notification.actionUrl && (
                            <DropdownMenuItem
                              onClick={() =>
                                window.open(notification.actionUrl, "_blank")
                              }
                            >
                              <AlertCircle className="h-4 w-4 mr-2" />
                              Xem chi tiết
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Create Notification Dialog */}
      {isAdmin && (
        <CreateNotificationDialog
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
          onSuccess={fetchNotifications}
        />
      )}
    </div>
  );
}
